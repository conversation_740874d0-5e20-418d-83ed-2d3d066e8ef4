package com.engine.parent.http.util;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.constrant.ApiResultCst;
import com.engine.parent.http.dto.ApiResult;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.dto.WeaverApiResult;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Getter;
import okhttp3.*;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;

import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/7/8-18:13
 * @description : REST请求工具类
 */
public class HttpUtil {
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    // 提供获取 OkHttpClient 单例的方法
    // 私有静态变量，用于存储单例的 OkHttpClient
    @Getter
    private static final OkHttpClient okHttpClientInstance;

    // 静态块，在类加载时初始化 OkHttpClient
    static {
        okHttpClientInstance = new OkHttpClient.Builder()
                .connectionPool(new ConnectionPool(20, 30, TimeUnit.MINUTES)) // 设置连接池大小和超时时间
                .connectTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .build();
    }

    // 私有构造函数，防止外部实例化
    private HttpUtil() {
    }


    /**
     * 发送post接口,效果等同postman发送raw格式数据
     * 使用basic加密
     * 不带header
     *
     * @param url       接口地址
     * @param bodyParam body参数map
     * @param username  basic认证的用户名
     * @param password  basic认证的密码
     * @return
     */
    public static RestResult postData(String url, Map<String, Object> bodyParam, String username, String password) {
        String bodyContent = JSONObject.toJSONString(bodyParam);
        return postData(url, bodyContent, username, password);
    }

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     * 使用basic加密
     * 不带header
     *
     * @param url
     * @param bodyParam 请求body，JSON对象格式的字符串
     * @param username  basic认证的用户名
     * @param password  basic认证的密码
     * @return
     */
    public static RestResult postData(String url,
                                      String bodyParam,
                                      String username,
                                      String password) {
        return postDataWithHeader(url, null, bodyParam, username, password);
    }

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     * 不带header
     *
     * @param url
     * @param bodyParam 请求body，JSON对象格式的字符串
     * @return
     */
    public static RestResult postData(String url, String bodyParam) {
        return postDataWithHeader(url, null, bodyParam);
    }

    /**
     * 发送post接口，使用application/x-www-form-urlencoded格式
     *
     * @param url
     * @param bodyParam
     * @return
     */
    public static RestResult postDataFormUrlEncoded(String url, Map<String, String> bodyParam) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
        return doPostForm(url, bodyParam, headers);
    }

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     * 带header
     *
     * @param url
     * @param bodyParam 请求body，JSON对象格式的字符串
     * @return
     */
    public static RestResult postDataWithHeader(String url,
                                                Map<String, String> headers,
                                                String bodyParam) {
        MediaType mediaType = MediaType.parse("application/json");
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("Content-Type", "application/json");
        return doPost(url, mediaType, bodyParam, headers);
    }

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     * 带header
     *
     * @param url
     * @param headers
     * @param bodyParam
     * @param username
     * @param password
     * @return
     */
    public static RestResult postDataWithHeader(String url,
                                                Map<String, String> headers,
                                                String bodyParam,
                                                String username,
                                                String password) {
        String credentials = Credentials.basic(username, password);
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("Authorization", credentials);
        return postDataWithHeader(url, headers, bodyParam);
    }


    /**
     * 发送GET接口,参数都在url上
     * 不带header
     *
     * @param url
     * @return
     */
    public static RestResult getData(String url) {
        return getDataWithHeader(url, null);
    }

    /**
     * 发送GET接口,参数都在url上
     * 带header
     *
     * @param url
     * @return
     */
    public static RestResult getDataWithHeader(String url, Map<String, String> headers) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        return doGet(url, headers);
    }

    /**
     * 发送GET接口,参数都在url上
     * 使用basic加密
     * 不带header
     *
     * @param url
     * @param username
     * @param password
     * @return
     */
    public static RestResult getData(String url, String username, String password) {
        return getDataWithHeader(url, null, username, password);
    }

    /**
     * 发送GET接口,参数都在url上
     * 使用basic加密
     * 带header
     *
     * @param url
     * @param username
     * @param password
     * @return
     */
    public static RestResult getDataWithHeader(String url,
                                               Map<String, String> headers,
                                               String username,
                                               String password) {
        String credentials = Credentials.basic(username, password);
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("Authorization", credentials);
        return doGet(url, headers);
    }

    /**
     * 发送post接口,效果等同postman发送xml格式数据
     * 不带header
     *
     * @param url      例如：ip:端口号/services/WorkflowService
     * @param xmlInput 整体的请求报文 文本
     * @return
     */
    public static RestResult postXmlData(String url, String xmlInput) {
        return postXmlDataWithHeader(url, null, xmlInput);
    }

    /**
     * 发送post接口,效果等同postman发送xml格式数据
     * 带header
     *
     * @param url      例如：ip:端口号/services/WorkflowService
     * @param xmlInput 整体的xml请求报文 文本
     * @param headers  请求头参数
     * @return
     */
    public static RestResult postXmlDataWithHeader(String url, Map<String, String> headers, String xmlInput) {
        MediaType mediaType = MediaType.parse("text/xml; charset=utf-8");
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("Content-Type", "text/xml;charset=UTF-8");
        headers.put("SOAPAction", "");
        return doPost(url, mediaType, xmlInput, headers);
    }


    //----以下是旧方法-----

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     *
     * @param url
     * @param params
     * @return
     */
    public static ApiResult postData(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            // 使用单例的 OkHttpClient
            OkHttpClient client = HttpUtil.getOkHttpClientInstance();
            MediaType mediaType = MediaType.parse("application/json");
            String aa = JSONObject.toJSONString(params, true);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(params, true));
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject jo = JSONObject.parseObject(responseBody.string());
                if (response.code() == ApiResultCst.API_SUCCESS_CODE) {
                    String code = jo.getString("code");
                    if (ApiResultCst.API_RESULT_SUCCESS.equals(code)) {
                        res.setSuccessFlag(true);
                    }
                    res.setData(jo.getString("data"));
                    res.setResMsg(jo.getString("msg"));
                } else {
                    res.setResMsg("调用失败，失败Code:" + response.code());
                }
            } else {
                res.setResMsg("调用失败，响应body为空");
            }

        } catch (Exception e) {
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 使用http调用泛微接口，格式application/x-www-form-urlencoded; charset=utf-8
     *
     * @param url           接口地址
     * @param appid         授权应用id appid
     * @param token         访问接口的token
     * @param encryptUserid RSA加密后的用户id
     * @param params        Map类型参数
     * @return
     * @throws Exception
     */
    public static String postWeaverData(String url,
                                        String appid,
                                        String token,
                                        String encryptUserid,
                                        Map<String, Object> params) throws Exception {
        // 使用单例的 OkHttpClient
        OkHttpClient client = HttpUtil.getOkHttpClientInstance();
        String value;
        FormBody.Builder bodyBuilder = new FormBody.Builder();

        //通过Map.entrySet遍历key和value
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            value = Util.null2String(entry.getValue());
            bodyBuilder.add(entry.getKey(), value);
        }
        FormBody requestBody = bodyBuilder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")
                .addHeader("appid", appid)
                .addHeader("token", token)
                .addHeader("userid", encryptUserid)
                .build();
        Response response = client.newCall(request).execute();
        ResponseBody responseBody = response.body();
        if (responseBody != null) {
            return responseBody.string();
        }
        return "";
    }


    /**
     * 使用http调用泛微接口，格式application/x-www-form-urlencoded; charset=utf-8
     *
     * @param url    接口地址
     * @param cookie cookie
     * @param params Map类型参数
     * @return
     * @throws Exception
     */
    public static String postWeaverDataWithCookie(String url,
                                                  String cookie,
                                                  Map<String, Object> params) throws Exception {
        // 使用单例的 OkHttpClient
        OkHttpClient client = HttpUtil.getOkHttpClientInstance();
        String value;
        FormBody.Builder bodyBuilder = new FormBody.Builder();
        //通过Map.entrySet遍历key和value
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            value = Util.null2String(entry.getValue());
            bodyBuilder.add(entry.getKey(), value);
        }
        FormBody requestBody = bodyBuilder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")
                .addHeader("Cookie", cookie)
                .build();
        Response response = client.newCall(request).execute();
        ResponseBody responseBody = response.body();
        if (responseBody != null) {
            return responseBody.string();
        }
        return "";
    }


    /**
     * 发送post接口,效果等同postman发送raw格式数据
     *
     * @param url
     * @param appid
     * @return
     */
    public synchronized static WeaverApiResult postWeaverApi(String url,
                                                             String appid,
                                                             String userid,
                                                             String token,
                                                             Map<String, Object> bodyParam) {
        BaseBean bb = new BaseBean();
        bb.writeLog("postWeaverApi --START");
        bb.writeLog("bodyParam :" + bodyParam);
        bb.writeLog("url :" + url);
        bb.writeLog("appid :" + appid);
        bb.writeLog("userid :" + userid);
        bb.writeLog("token :" + token);
        WeaverApiResult res = new WeaverApiResult();
        try {
            String data = postWeaverData(url, appid, token, userid, bodyParam);
            bb.writeLog("postWeaverData --END");
            bb.writeLog("postWeaverData data :" + data);
            if (!data.isEmpty()) {
                JSONObject jo = JSONObject.parseObject(data);
                res.setCode(Util.null2String(jo.get("code")));
                res.setData(Util.null2String(jo.get("data")));
                if (jo.getJSONObject("errMsg").isEmpty()) {
                    res.setErrMsg("");
                } else {
                    res.setErrMsg(jo.getJSONObject("errMsg").toJSONString());
                }
            } else {
                res.setCode("NET_ERRO");
                res.setErrMsg("调用失败，未获取到返回结果");
            }
        } catch (Exception e) {
            res.setCode("SYSTEM_INNER_ERROR");
            res.setErrMsg("执行异常：" + e.getMessage() + ";" + Arrays.toString(e.getStackTrace()));
            bb.writeLog("catch exception：" + e.getMessage() + ";" + Arrays.toString(e.getStackTrace()));
        }
        bb.writeLog("postWeaverApi --END");
        return res;
    }

    /**
     * 发送get接口,效果等同postman发送raw格式数据
     *
     * @param url
     * @param params
     * @return
     */
    public static ApiResult getDataWithGBK(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .method("GET", null)
                    .addHeader("Content-Type", "application/json;charset=utf-8")
                    .build();
            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                String gbkValue = new String(responseBody.bytes(), "GBK");
                JSONObject jo = JSONObject.parseObject(gbkValue);
                if (response.code() == ApiResultCst.API_SUCCESS_CODE) {
                    res.setSuccessFlag(true);
                    res.setData(jo);
                } else {
                    res.setResMsg("调用失败，失败Code:" + response.code());
                }
            } else {
                res.setResMsg("调用失败，响应body为空");
            }
        } catch (Exception e) {
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 做POST请求
     * from格式的body
     *
     * @param url      接口地址
     * @param bodyForm
     * @param headers
     * @return
     */
    private static RestResult doPostForm(String url, Map<String, String> bodyForm, Map<String, String> headers) {
        RestResult result = new RestResult();
        RestResult.RequestInfo requestInfo = new RestResult.RequestInfo();
        RestResult.ResponseInfo responseInfo = new RestResult.ResponseInfo();
        //设置请求开始时间
        String beginTimeStr = TimeUtil.getCurrentTimeString();
        result.setStartTime(beginTimeStr);
        log.info("doPostForm start---url:" + url + ",headers:" + headers);
        try {
            //设置请求方法
            requestInfo.setMethod("POST");
            //设置请求url
            requestInfo.setUrl(url);
            //构建Form表单请求body
            FormBody.Builder bodyBuilder = new FormBody.Builder();

            //通过Map.entrySet遍历key和value
            for (Map.Entry<String, String> entry : bodyForm.entrySet()) {
                String value = Util.null2String(entry.getValue());
                bodyBuilder.add(entry.getKey(), value);
            }
            FormBody requestBody = bodyBuilder.build();

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(requestBody);
            //step2: 添加header
            if (!headers.isEmpty()) {
                // 遍历HashMap并将每个键值对作为header添加到请求中
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    requestBuilder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            //step3: 组装为Request对象
            Request request = requestBuilder.build();
            //step4 : 使用单例的 OkHttpClient，调用接口
            OkHttpClient client = HttpUtil.getOkHttpClientInstance();
            Response response = client.newCall(request).execute();

            //记录响应信息
            responseInfo.setCode(response.code());
            responseInfo.setMessage(response.message());
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                try (InputStream inputStream = responseBody.byteStream()) {
                    byte[] buffer = new byte[8192];  // 设置缓冲区大小为 8KB
                    int bytesRead;
                    StringBuilder responseStrBuilder = new StringBuilder();
                    while (true) {
                        try {
                            bytesRead = inputStream.read(buffer);
                            if (bytesRead == -1) {
                                break;  // 到达流的末尾
                            }
                            responseStrBuilder.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                        } catch (EOFException eofException) {
                            log.warn("Reached end of stream unexpectedly: " + eofException.getMessage());
                            break;  // 捕获 EOFException，安全地退出循环
                        }
                    }
                    String responseBodyStr = responseStrBuilder.toString();
                    responseInfo.setBody(responseBodyStr);
                    if (response.isSuccessful()) {
                        result.setSuccess(true);
                        result.setMsg("Request success！");
                    } else {
                        result.setSuccess(false);
                        result.setMsg("Request failed: " + response.code() + " - " + response.message() + "-" + responseBodyStr);
                    }
                } catch (IOException e) {
                    result.setSuccess(false);
                    result.setMsg("读取分块传输时出现异常: " + e.getMessage());
                    log.error("读取分块传输时出现异常", e);
                }
            } else {
                result.setSuccess(false);
                result.setMsg("未获取到响应body");
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMsg("Request exception: " + SDUtil.getExceptionDetail(e));
            responseInfo.setCode(-1);
            responseInfo.setMessage("Request exception: " + SDUtil.getExceptionDetail(e));
        }
        //设置请求信息
        result.setRequestInfo(requestInfo);
        //设置响应信息
        result.setResponseInfo(responseInfo);
        String entTimeStr = TimeUtil.getCurrentTimeString();

        result.setEndTime(entTimeStr);
        result.setDuration(String.valueOf(SDTimeUtil.timeDifferInSeconds(beginTimeStr, entTimeStr)));
        log.info("doPostForm end----isSuccess:" + result.isSuccess() + ",responseInfo:" + result.getResponseInfo() + ",starttime:" + beginTimeStr + ",endtime:" + entTimeStr);
        return result;
    }

    /**
     * 做POST请求
     *
     * @param url         接口地址
     * @param mediaType
     * @param bodyContent
     * @param headers
     * @return
     */
    private static RestResult doPost(String url, MediaType mediaType, String bodyContent, Map<String, String> headers) {
        RestResult result = new RestResult();
        RestResult.RequestInfo requestInfo = new RestResult.RequestInfo();
        RestResult.ResponseInfo responseInfo = new RestResult.ResponseInfo();
        //设置请求开始时间
        String beginTimeStr = TimeUtil.getCurrentTimeString();
        result.setStartTime(beginTimeStr);
        log.info("doPost start---url:" + url + ",headers:" + headers);
        try {
            //设置请求方法
            requestInfo.setMethod("POST");
            //设置请求url
            requestInfo.setUrl(url);
            //设置请求body参数
            requestInfo.setBody(bodyContent);

            //step1 : 组装请求body
            RequestBody body = RequestBody.create(mediaType, bodyContent);
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(body);
            //step2: 添加header
            if (!headers.isEmpty()) {
                // 遍历HashMap并将每个键值对作为header添加到请求中
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    requestBuilder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            //step3: 组装为Request对象
            Request request = requestBuilder.build();
            //step4 : 使用单例的 OkHttpClient，调用接口
            OkHttpClient client = HttpUtil.getOkHttpClientInstance();
            Response response = client.newCall(request).execute();

            //记录响应信息
            responseInfo.setCode(response.code());
            responseInfo.setMessage(response.message());
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                try (InputStream inputStream = responseBody.byteStream()) {
                    byte[] buffer = new byte[8192];  // 设置缓冲区大小为 8KB
                    int bytesRead;
                    StringBuilder responseStrBuilder = new StringBuilder();
                    while (true) {
                        try {
                            bytesRead = inputStream.read(buffer);
                            if (bytesRead == -1) {
                                break;  // 到达流的末尾
                            }
                            responseStrBuilder.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                        } catch (EOFException eofException) {
                            log.warn("Reached end of stream unexpectedly: " + eofException.getMessage());
                            break;  // 捕获 EOFException，安全地退出循环
                        }
                    }
                    String responseBodyStr = responseStrBuilder.toString();
                    responseInfo.setBody(responseBodyStr);
                    if (response.isSuccessful()) {
                        result.setSuccess(true);
                        result.setMsg("Request success！");
                    } else {
                        result.setSuccess(false);
                        result.setMsg("Request failed: " + response.code() + " - " + response.message() + "-" + responseBodyStr);
                    }
                } catch (IOException e) {
                    result.setSuccess(false);
                    result.setMsg("读取分块传输时出现异常: " + e.getMessage());
                    log.error("读取分块传输时出现异常", e);
                }
            } else {
                result.setSuccess(false);
                result.setMsg("未获取到响应body");
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMsg("Request exception: " + SDUtil.getExceptionDetail(e));
            responseInfo.setCode(-1);
            responseInfo.setMessage("Request exception: " + SDUtil.getExceptionDetail(e));
        }
        //设置请求信息
        result.setRequestInfo(requestInfo);
        //设置响应信息
        result.setResponseInfo(responseInfo);
        String entTimeStr = TimeUtil.getCurrentTimeString();

        result.setEndTime(entTimeStr);
        result.setDuration(String.valueOf(SDTimeUtil.timeDifferInSeconds(beginTimeStr, entTimeStr)));
        log.info("doPost end----isSuccess:" + result.isSuccess() + ",responseInfo:" + result.getResponseInfo() + ",starttime:" + beginTimeStr + ",endtime:" + entTimeStr);
        return result;
    }

    /**
     * 做GET请求
     *
     * @param url
     * @param headers
     * @return
     */
    private static RestResult doGet(String url, Map<String, String> headers) {
        RestResult result = new RestResult();
        RestResult.RequestInfo requestInfo = new RestResult.RequestInfo();
        RestResult.ResponseInfo responseInfo = new RestResult.ResponseInfo();
        String beginTimeStr = TimeUtil.getCurrentTimeString();
        result.setStartTime(beginTimeStr);
        log.info("doGet start");
        log.info("url:" + url);
        log.info("headers:" + headers);
        try {
            //设置请求方法
            requestInfo.setMethod("GET");
            //设置请求url
            requestInfo.setUrl(url);
            //get请求默认为text/plain
            //step1 : 组装请求Builder
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url);
            //step2: 添加header
            if (!headers.isEmpty()) {
                // 遍历HashMap并将每个键值对作为header添加到请求中
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    requestBuilder.addHeader(entry.getKey(), entry.getValue());
                }
            }
            //step3: 组装为Request对象
            Request request = requestBuilder.build();
            //step4 : 使用单例的 OkHttpClient，调用接口
            OkHttpClient client = HttpUtil.getOkHttpClientInstance();
            Response response = client.newCall(request).execute();
            //记录响应信息
            responseInfo.setCode(response.code());
            responseInfo.setMessage(response.message());
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                try (InputStream inputStream = responseBody.byteStream()) {
                    byte[] buffer = new byte[8192];  // 设置缓冲区大小为 8KB
                    int bytesRead;
                    StringBuilder responseStrBuilder = new StringBuilder();
                    while (true) {
                        try {
                            bytesRead = inputStream.read(buffer);
                            if (bytesRead == -1) {
                                break;  // 到达流的末尾
                            }
                            responseStrBuilder.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                        } catch (EOFException eofException) {
                            log.warn("Reached end of stream unexpectedly: " + eofException.getMessage());
                            break;  // 捕获 EOFException，安全地退出循环
                        }
                    }
                    String responseBodyStr = responseStrBuilder.toString();
                    responseInfo.setBody(responseBodyStr);
                    if (response.isSuccessful()) {
                        result.setSuccess(true);
                        result.setMsg("Request success！");
                    } else {
                        result.setSuccess(false);
                        result.setMsg("Request failed: " + response.code() + " - " + response.message() + "-" + responseBodyStr);
                    }
                } catch (IOException e) {
                    result.setSuccess(false);
                    result.setMsg("读取分块传输时出现异常: " + e.getMessage());
                    log.error("读取分块传输时出现异常", e);
                }
            } else {
                result.setSuccess(false);
                result.setMsg("未获取到响应body");
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMsg("Request exception: " + SDUtil.getExceptionDetail(e));
            responseInfo.setCode(-1);
            responseInfo.setMessage("Request exception: " + SDUtil.getExceptionDetail(e));
        }
        //设置请求信息
        result.setRequestInfo(requestInfo);
        //设置响应信息
        result.setResponseInfo(responseInfo);

        String entTimeStr = TimeUtil.getCurrentTimeString();
        result.setEndTime(entTimeStr);
        result.setDuration(String.valueOf(SDTimeUtil.timeDifferInSeconds(beginTimeStr, entTimeStr)));
        log.info("doGet end----isSuccess:" + result.isSuccess() + ",responseInfo:" + result.getResponseInfo() + ",starttime:" + beginTimeStr + ",endtime:" + entTimeStr);

        return result;
    }

    public static void main(String[] args) {
        String url = "https://api15.sapsf.cn/odata/v2/Position?$format=json&$select=code,externalName_zh_CN,cust_country,effectiveStatus&$filter=cust_country eq 'WallabyChina'";
        String userName = "OAAPIadmin@shanghaiwaD";
        String password = "Sf@11114";
        Map<String, Object> params = new HashMap<>();
        RestResult restResult = getData(url, userName, password);
        System.out.println(restResult);
    }
}
