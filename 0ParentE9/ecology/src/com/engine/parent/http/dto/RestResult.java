package com.engine.parent.http.dto;

import lombok.Data;

/**
 * @FileName RestResult
 * @Description Rest请求结果
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/8/14
 */
@Data
public class RestResult {
    /**
     * 是否调用成功（只代表接口返回200成功，实际业务成功需要根据ResponseInfo自行判断）
     */
    private boolean isSuccess;
    /**
     * 信息
     */
    private String msg;
    /**
     * 请求时间
     */
    private String startTime;
    /**
     * 响应时间
     */
    private String endTime;
    /**
     * 接口花费的时间 毫秒 1000ms= 1s
     */
    private String duration;
    /**
     * 请求报文信息
     */
    private RequestInfo requestInfo;
    /**
     * 响应报文信息
     */
    private ResponseInfo responseInfo;

    @Data
    public static class RequestInfo {
        /**
         * 请求方法名
         */
        private String method;
        /**
         * 请求地址
         */
        private String url;
        /**
         * 请求body
         */
        private String body;
    }

    @Data
    public static class ResponseInfo {
        /**
         * 响应code
         */
        private Integer code;
        /**
         * 响应消息
         */
        private String message;
        /**
         * 响应body
         */
        private String body;

    }
}
