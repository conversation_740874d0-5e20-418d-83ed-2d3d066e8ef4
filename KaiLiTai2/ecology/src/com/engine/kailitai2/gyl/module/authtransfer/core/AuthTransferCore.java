package com.engine.kailitai2.gyl.module.authtransfer.core;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.kailitai2.gyl.module.authtransfer.bean.AuthTransferConfig;
import com.engine.parent.common.JersyApiCoreBase;
import com.engine.parent.common.util.EncryptUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.time.util.SDTimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName AuthTransferCore.java
 * @Description 建模权限转移
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/2
 */
public class AuthTransferCore extends JersyApiCoreBase {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询建模数据pageSize
     */
    private final int pageSize = 1000;
    /**
     * 配置列表
     */
    private List<AuthTransferConfig> configList;


    public AuthTransferCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        configList = null;
    }

    @Override
    public Map<String, Object> execute() {
        try {
            //初始化
            super.initBase(this.getClass().getSimpleName(), this.getClass().getName(), "建模权限转移");
            //校验参数
            checkParam();
            if (!error.isEmpty()) {
                return handleResult();
            }
            //读取配置表
            configList = AuthTransferConfig.getConfigList();
            if (configList == null || configList.isEmpty()) {
                appendLog("没有配置信息，不处理");
                return handleResult();
            }
            //多线程执行每个配置信息
            //方式2：使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
            ExecutorService executorService = Executors.newFixedThreadPool(5);
            CountDownLatch latch = new CountDownLatch(configList.size());
            for (AuthTransferConfig eachConfig : configList) {
                executorService.submit(() -> {
                    try {
                        //处理明细的每一行数据
                        processEachConfig(eachConfig);
                    } finally {
                        latch.countDown(); // 线程完成任务后减少计数器
                    }
                });
            }
            try {
                // 等待所有线程完成任务，超时60分钟
                if (!latch.await(60, TimeUnit.MINUTES)) {
                    appendLog("超时未执行完所有线程任务，请检查!");
                }
            } catch (InterruptedException e) {
                log.error("InterruptedException error", e);
                appendLog("latch.await() 出错：" + e.getMessage());
            } finally {
                executorService.shutdown();
            }
            //执行所有任务后，往下走其他业务逻辑...

        } catch (Exception e) {
            log.error("执行异常", e);
        }
        return handleResult();
    }

    /**
     * 校验参数
     */
    private void checkParam() {
        List<String> missedParams = new ArrayList<>();
        String needParams = "fromUser,toUser";
        for (String key : needParams.split(",")) {
            String value = Util.null2String(params.get(key));
            if (value.isEmpty()) {
                missedParams.add(key);
            }
        }
        if (!missedParams.isEmpty()) {
            error = "缺失参数:" + missedParams;
        }
    }

    /**
     * 执行每个配置
     *
     * @param eachConfig
     */
    private void processEachConfig(AuthTransferConfig eachConfig) {
        //接口地址
        String cubeApiAddress = Util.null2String(eachConfig.getJmcxjkdz());
        //查询第一页数据
        Map<String, String> bodyParam = buildApiParam(eachConfig, 1);

        RestResult restResult = HttpUtil.postDataFormUrlEncoded(cubeApiAddress, bodyParam);
        appendLog("接口响应:" + JSONObject.toJSONString(restResult));
        if (restResult.isSuccess()) {
            //获取查询接口中的id值集合
            Set<String> ids = getAuthDataIds(restResult);
            //TODO 需要分页查询接口，1页查询1000调
        }


    }


    private Map<String, String> buildApiParam(AuthTransferConfig eachConfig, int pageNo) {
        Map<String, String> bodyParam = new HashMap<>();

        String systemid = Util.null2String(eachConfig.getJksqbs());
        String psw = Util.null2String(eachConfig.getJksqmm());

        //转移前人员id
        String fromUser = Util.null2String(params.get("fromUser"));

        JSONObject datajson = new JSONObject();
        //操作人信息
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("operator", fromUser);
        datajson.put("operationinfo", operationinfo);

        datajson.put("mainTable", new JSONObject());

        JSONObject pageInfo = new JSONObject();
        pageInfo.put("pageNo", pageNo + "");
        pageInfo.put("pageSize", pageSize + "");
        datajson.put("pageInfo", pageInfo);

        JSONObject header = new JSONObject();
        String currentDateTime = SDTimeUtil.getCurrentTimeStringOnlyNumnbers();
        String beforePsw = systemid + psw + currentDateTime;
        appendLog("加密前的值:" + beforePsw);
        String afterPsw = EncryptUtil.md5(beforePsw);
        appendLog("加密后的值:" + afterPsw);
        header.put("systemid", systemid);
        header.put("currentDateTime", currentDateTime);
        header.put("Md5", afterPsw);
        datajson.put("header", header);


        bodyParam.put("datajson", datajson.toJSONString());
        return bodyParam;
    }

    /**
     * 解析接口响应中所有的id集合set
     *
     * @param restResult
     * @return
     */
    private Set<String> getAuthDataIds(RestResult restResult) {
        Set<String> ids = new HashSet<>();
        JSONObject jo, mainTable;
        String eachId;
        if (restResult.getResponseInfo() != null) {
            String responseBody = Util.null2String(restResult.getResponseInfo().getBody());
            JSONObject responseBodyJson = null;
            try {
                responseBodyJson = JSONObject.parseObject(responseBody);
            } catch (Exception e) {
                log.error("响应结果格式异常", e);
            }
            if (responseBodyJson != null) {
                if (responseBodyJson.containsKey("result")) {
                    JSONArray resultArray = responseBodyJson.getJSONArray("result");
                    if (resultArray != null && !resultArray.isEmpty()) {
                        for (int i = 0; i < resultArray.size(); i++) {
                            jo = resultArray.getJSONObject(i);
                            if (jo.containsKey("mainTable")) {
                                mainTable = jo.getJSONObject("mainTable");
                                if (mainTable.containsKey("id")) {
                                    eachId = Util.null2String(mainTable.get("id"));
                                    if (!eachId.isEmpty()) {
                                        ids.add(eachId);
                                    }
                                }
                            }
                        }
                    } else {
                        log.warn("当前查询结果没有result了，代表该页数据结束");
                    }
                }
            }
        }
        return ids;
    }
}
