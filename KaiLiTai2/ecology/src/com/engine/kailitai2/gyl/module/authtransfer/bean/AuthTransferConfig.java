package com.engine.kailitai2.gyl.module.authtransfer.bean;

import com.engine.parent.query.util.QueryUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;

import java.util.List;

@Getter
@Setter
/**
 * @FileName AuthTransferConfig.java
 * @Description 权限转移配置
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/3
 */
public class AuthTransferConfig {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 建模表名
     */
    private String jmbm;
    /**
     * 建模模块id
     */
    private Integer jmmkid;
    /**
     * 转移后权限类型
     */
    private Integer qxlx;
    /**
     * 建模查询接口地址(全地址)
     */
    private String jmcxjkdz;
    /**
     * 接口授权标识
     */
    private String jksqbs;
    /**
     * 接口授权密码
     */
    private String jksqmm;

    /**
     * 建模表名
     */
    public static final String TABLE_NAME = "uf_qxzypz";


    /**
     * 获取配置列表
     *
     * @return
     */
    public static List<AuthTransferConfig> getConfigList() {
        String sql = "select * from " + TABLE_NAME + " where sfqy = 1";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObjList(rs, AuthTransferConfig.class);
        }
        return null;
    }


}
